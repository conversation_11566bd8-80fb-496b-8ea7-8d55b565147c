# Superdiary ❤️

<div align="left">
    <img src = "https://github.com/Rafsanjani/superdiary/actions/workflows/android_pr.yml/badge.svg"  alt="Link to CI build status"/>
    <img src = "https://img.shields.io/endpoint?url=https://gist.githubusercontent.com/rafsanjani/129c8d4608ea11b81849152dba085532/raw/superdiary-badge.json" alt="link to coverage status"/>
</div>
A simple diary app, mostly focused on quality architectural patterns and improved testing strategies (unit, snapshot and instrumentation).

### Android - Light

| Dashboard                                                                                                  | Create Diary                                                                                      |
|------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------|
|  <img width="250" alt="Screenshot 2023-12-15 at 11 16 22" src="https://github.com/rafsanjani/superdiary/assets/9197459/192494b8-162f-4cc2-a416-aebe5ea11da8"> |<img width="250" alt="Screenshot 2023-12-15 at 11 16 22" src="https://github.com/rafsanjani/superdiary/assets/9197459/a5f608c5-d526-41f4-b382-7abfbaea1b9f"> |






### Android - Dark

| Dashboard                                                                                                        | Diary AI                                                                                                    |
|----------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------|
|<img width="250" src="https://github.com/rafsanjani/superdiary/assets/9197459/f84e64fd-daba-4663-ba8e-55a2a27b7280" /> | <img width="250" src="https://github.com/rafsanjani/superdiary/assets/9197459/cc10b064-9b1a-43be-8bcd-53e9d9fbbfa5" /> |

### iOS - Dark

| Diary AI                                                                                                                                                    | Dashboard                                                                                                                                                    |
|--------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------|
| <img width="250" alt="Screenshot 2023-12-15 at 11 16 22" src="https://github.com/rafsanjani/superdiary/assets/9197459/a736d7b0-ed27-422b-af4e-58317a7af47d"> | <img width="250" alt="Screenshot 2023-12-15 at 11 21 01" src="https://github.com/rafsanjani/superdiary/assets/9197459/98c012b9-3690-4148-a911-9a0ea1ac51fc"> |

### iOS - Light

| Dashboard                                                                                                                                                    | Diary AI                                                                                                                                                     |
|--------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------|
| <img width="250" alt="Screenshot 2023-12-15 at 11 24 10" src="https://github.com/rafsanjani/superdiary/assets/9197459/d81df92c-69b8-4a6c-8717-d5959250afe7"> | <img width="250" alt="Screenshot 2023-12-15 at 11 24 17" src="https://github.com/rafsanjani/superdiary/assets/9197459/1ce75c99-e498-4e66-afc0-a32ac4bac8f2"> |

### Status: 🚧 In progress

<p>Superdiary is still under heavy development and some screens are yet to be designed. This app is highly experimental and meant to serve as my personal playground for Kotlin multiplatform </p>

### License

```
MIT License

Copyright (c) 2024 Rafsanjani Aziz

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```
