name: Sync main with develop

on:
  schedule:
    - cron: '0 0 * * 0'

jobs:
  build:
    runs-on: macos-14
    steps:
      - uses: actions/checkout@v4
        with:
          lfs: true

      - name: Sync main with develop branch
        uses: peter-evans/create-pull-request@v7
        with:
            token: ${{ secrets.SECRET_GITHUB }}
            base: main
            head: development
            title: Sync main with develop
            body: Sync main with develop branch
