name: Multiplatform Build

on:
    pull_request:
concurrency:
    group: ${{ github.workflow }}-${{ github.ref }}
    cancel-in-progress: true

jobs:
    danger:
        name: Run <PERSON> (Ktlint, android lint)
        runs-on: macos-14
        steps:
            -   uses: actions/checkout@v4
            -   name: Create LFS file list
                run: git lfs ls-files -l | cut -d' ' -f1 | sort > .lfs-assets-id

            -   name: Restore LFS cache
                uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
                with:
                    path: .git/lfs
                    key: ${{runner.os}}-lfs-${{hashFiles('.lfs-assets-id')}}
            -   name: Git LFS Pull
                run: GIT_TRACE=1 git lfs pull

            -   uses: actions/setup-java@v4
                with:
                    distribution: 'zulu'
                    java-version: '17'

            -   name: Setup Gradle
                uses: gradle/actions/setup-gradle@v4

            -   name: Danger Checks
                run: |
                    gem install bundler
                    bundle install
                    bundle exec danger
                env:
                    DANGER_GITHUB_API_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    snapshot:
        name: Snapshot test with paparazzi
        env:
            BUILD_NUMBER: ${{github.event.number}}
        runs-on: macos-14
        needs: danger
        steps:
            -   uses: actions/checkout@v4
            -   name: Restore LFS cache
                uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
                with:
                    path: .git/lfs
                    key: ${{runner.os}}-lfs-${{hashFiles('.lfs-assets-id')}}
            -   name: Git LFS Pull
                run: GIT_TRACE=1 git lfs pull

            -   uses: actions/setup-java@v4
                with:
                    distribution: 'zulu'
                    java-version: '17'

            -   name: Setup Gradle
                uses: gradle/actions/setup-gradle@v4

            -   name: Snapshot test (shared-ui)
                run: ./gradlew verifyPaparazziDebug --continue

            -   name: Create paparazzi report
                if: failure()
                run: ./create_paparazzi_report.sh

            -   name: Comment snapshot difference markdown file on PR
                uses: thollander/actions-comment-pull-request@v3
                if: failure()
                with:
                    file-path: snapshots.md
                    github-token: ${{secrets.GITHUB_TOKEN}}
                    comment-tag: snapshots
                    mode: recreate

    iosTest:
        name: Run iOS unit tests
        env:
            SENTRY_BASE_URL_ANDROID: ${{secrets.SENTRY_BASE_URL_ANDROID}}
            SENTRY_AUTH_TOKEN: ${{secrets.SENTRY_AUTH_TOKEN}}
        runs-on: macos-latest
        needs: danger
        steps:
            -   uses: actions/checkout@v4
            -   name: Restore LFS cache
                uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
                with:
                    path: .git/lfs
                    key: ${{runner.os}}-lfs-${{hashFiles('.lfs-assets-id')}}
            -   name: Git LFS Pull
                run: GIT_TRACE=1 git lfs pull

            -   uses: actions/setup-java@v4
                with:
                    distribution: 'zulu'
                    java-version: '17'

            -   name: Setup Gradle
                uses: gradle/actions/setup-gradle@v4

            -   name: Unit tests iOS
                run: ./gradlew iosSimulatorArm64Test

    jvmTest:
        name: Run JVM unit tests and generate coverage badge
        needs: danger
        env:
            SENTRY_BASE_URL_ANDROID: ${{secrets.SENTRY_BASE_URL_ANDROID}}
            SENTRY_AUTH_TOKEN: ${{secrets.SENTRY_AUTH_TOKEN}}
        runs-on: ubuntu-latest
        steps:
            -   uses: actions/checkout@v4
            -   name: Set up JDK
                uses: actions/setup-java@v4
                with:
                    java-version: '17'
                    distribution: 'zulu'

            -   name: Run JVM unit tests
                run: ./gradlew testDebugUnitTest -Pkover koverXmlReport

            -   name: Add coverage Report to PR
                id: jacoco
                uses: madrapps/jacoco-report@v1.7.2
                with:
                    paths: |
                        ${{ github.workspace }}/build/reports/kover/report.xml
                    token: ${{ secrets.GITHUB_TOKEN }}
                    update-comment: true
                    title: Code Coverage
                    min-coverage-overall: 92
                    min-coverage-changed-files: 85

            -   name: Print code coverage info
                run: |
                    echo "Total coverage ${{ steps.jacoco.outputs.coverage-overall }}"
                    echo "Changed Files coverage ${{ steps.jacoco.outputs.coverage-changed-files }}"


            -   name: Post changed files coverage violation
                if: ${{ steps.jacoco.outputs.coverage-changed-files  < 80.0 }}
                uses: actions/github-script@v7
                with:
                    script: |
                        core.setFailed('Coverage for changed files ${{steps.jacoco.outputs.coverage-changed-files}} is less than 80%!')

            #  -   name: Verify minimum code coverage (90%)
            #      run: ./gradlew testDebugUnitTest -Pkover koverVerify

            -   name: Post overall coverage violation
                if: ${{ steps.jacoco.outputs.coverage-overall  < 90.0 }}
                uses: actions/github-script@v7
                with:
                    script: |
                        core.setFailed('Coverage for total project: ${{steps.jacoco.outputs.coverage-overall}} is less than 90%!')


            -   name: Update dynamic badge gist
                if: github.ref == 'refs/heads/development'
                uses: schneegans/dynamic-badges-action@v1.7.0
                with:
                    auth: ${{secrets.SECRET_GITHUB_GIST}}
                    gistID: 129c8d4608ea11b81849152dba085532
                    filename: superdiary-badge.json
                    label: coverage
                    message: ${{ steps.jacoco.outputs.coverage-overall }}
                    valColorRange: ${{ steps.jacoco.outputs.coverage-overall }}
                    minColorRange: 0
                    maxColorRange: 100

    androidRelease:
        name: Build android app (release)
        runs-on: macos-14
        needs: jvmTest
        env:
            SENTRY_BASE_URL_ANDROID: ${{secrets.SENTRY_BASE_URL_ANDROID}}
            SENTRY_AUTH_TOKEN: ${{secrets.SENTRY_AUTH_TOKEN}}
            GOOGLE_SERVER_CLIENT_ID: ${{secrets.GOOGLE_SERVER_CLIENT_ID}}
            MAPS_API_KEY_ANDROID: ${{secrets.MAPS_API_KEY_ANDROID}}
            OPENAI_KEY: ${{secrets.OPENAI_KEY}}
            STORE_FILE_BASE64: ${{secrets.SIGNING_KEY_BASE64}}
            STORE_PASSWORD: ${{secrets.SIGNING_KEYSTORE_PASSWORD}}
            KEY_ALIAS: ${{secrets.SIGNING_KEY_ALIAS}}
            KEY_PASSWORD: ${{secrets.SIGNING_KEY_PASSWORD}}
            SUPABASE_URL: ${{secrets.SUPABASE_URL}}
            SUPABASE_KEY: ${{secrets.SUPABASE_KEY}}
            GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        steps:
            -   uses: actions/checkout@v4
            -   name: Restore LFS cache
                uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
                with:
                    path: .git/lfs
                    key: ${{runner.os}}-lfs-${{hashFiles('.lfs-assets-id')}}
            -   name: Git LFS Pull
                run: GIT_TRACE=1 git lfs pull

            -   uses: actions/setup-java@v4
                with:
                    distribution: 'zulu'
                    java-version: '17'

            -   name: Setup secrets.properties and Configurations/Release.xcconfig
                run: ./scripts/environments.sh

            -   name: Decode keystore file from secrets
                run: echo "$STORE_FILE_BASE64" | base64 --decode > keystore

            -   name: Inject Signing params into gradle.properties
                run: |
                    echo STORE_FILE=keystore >> gradle.properties
                    echo STORE_PASSWORD="$STORE_PASSWORD" >> gradle.properties
                    echo KEY_PASSWORD="$KEY_PASSWORD" >> gradle.properties
                    echo KEY_ALIAS="$KEY_ALIAS" >> gradle.properties
                    cat gradle.properties

            -   name: Setup Gradle
                uses: gradle/actions/setup-gradle@v4

            -   name: Build Android App (Release)
                run: ./gradlew androidApp:app:assembleRelease

            -   name: Get latest artifact ID
                run: |
                    ARTIFACT_ID=$(gh api repos/rafsanjani/superdiary/actions/artifacts \
                      --jq '.artifacts | sort_by(.updated_at) | last | .id')
                    echo "ARTIFACT_ID=$ARTIFACT_ID" >> $GITHUB_ENV

            -   name: Download the artifact
                run: |
                    gh api repos/rafsanjani/superdiary/actions/artifacts/$ARTIFACT_ID/zip > artifact.zip
                    unzip artifact.zip

            -   name: Run APK Diff
                run: |
                    gh release download -p '*.zip' -R jakewharton/diffuse 0.3.0
                    unzip diffuse-0.3.0.zip
                    ./diffuse-0.3.0/bin/diffuse diff app-release.apk androidApp/app/build/outputs/apk/release/app-release.apk > apk_differences.txt
                    { echo "\`\`\`"; head -n 17 apk_differences.txt; echo "\`\`\`"; echo; } >> apk_differences_summary.txt

            -   name: Post APK diff to Pull Request
                uses: thollander/actions-comment-pull-request@v3
                with:
                    file-path: apk_differences_summary.txt
                    github-token: ${{secrets.GITHUB_TOKEN}}
                    comment-tag: apk_difference

#
#    iOSReleaseBuild:
#        name: Build iOS app (Release)
#        runs-on: macos-14
#        #        needs: iosTest
#        steps:
#            -   uses: actions/checkout@v4
#                with:
#                    lfs: true
#            -   uses: actions/setup-java@v4
#                with:
#                    distribution: 'zulu'
#                    java-version: '17'
#
#            -   name: Setup secrets.properties and Configurations/Release.xcconfig
#                run: ./environments.sh
#
#            -   name: Setup Gradle
#                uses: gradle/actions/setup-gradle@v4
#
#            -   uses: maxim-lobanov/setup-xcode@v1
#                with:
#                    xcode-version: latest-stable
#
#            -   name: Build iOS App (Release)
#                run: |
#                    cd iosApp
#                    cat Configurations/Release.xcconfig
#                    xcodebuild \
#                        -scheme iosApp \
#                        -configuration Debug \
#                        -destination 'generic/platform=iOS' \
#                        build
#
