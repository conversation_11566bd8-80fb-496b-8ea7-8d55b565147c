package com.kapp.ifont.ui.di

import com.foreverrafs.superdiary.common.utils.di.utilsModule
import com.foreverrafs.superdiary.core.analytics.AnalyticsTracker
import com.foreverrafs.superdiary.core.logging.AggregateLogger
import com.foreverrafs.superdiary.di.platformModule
import com.kapp.ifont.ui.AppViewModel
import com.kapp.ifont.core.sync.di.fontSyncModule
import com.kapp.ifont.di.fontUseCaseModule
import org.koin.core.module.Module
import org.koin.core.module.dsl.viewModelOf
import org.koin.dsl.module

internal val screensModule: Module = module {
    viewModelOf(::AppViewModel)
}

/** This is the only component that is exposed outside of this module */
fun compositeModule(
    analytics: AnalyticsTracker,
    logger: AggregateLogger,
): List<Module> = listOf(
    utilsModule,
    screensModule,
    platformModule(analyticsTracker = analytics, aggregateLogger = logger),
    fontUseCaseModule,
    fontSyncModule,
)
