// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		058557BB273AAA24004C7B11 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 058557BA273AAA24004C7B11 /* Assets.xcassets */; };
		058557D9273AAEEB004C7B11 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 058557D8273AAEEB004C7B11 /* Preview Assets.xcassets */; };
		2152FB042600AC8F00CF470E /* iOSApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2152FB032600AC8F00CF470E /* iOSApp.swift */; };
		58033CFC2D437DB700A97874 /* BiometricAuth.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58033CFB2D437DB700A97874 /* BiometricAuth.swift */; };
		58072E372CDD90C000B59193 /* GoogleTokenProviderImpl.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58072E362CDD90C000B59193 /* GoogleTokenProviderImpl.swift */; };
		587B21362CDC0B6B0081E14F /* GoogleSignIn in Frameworks */ = {isa = PBXBuildFile; productRef = 587B21352CDC0B6B0081E14F /* GoogleSignIn */; };
		587B21382CDC0B6B0081E14F /* GoogleSignInSwift in Frameworks */ = {isa = PBXBuildFile; productRef = 587B21372CDC0B6B0081E14F /* GoogleSignInSwift */; };
		58A8DBF22CA21EC60084B125 /* Environment.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58A8DBF12CA21EC60084B125 /* Environment.swift */; };
		58DACF202C9E2ECE00664937 /* Debug.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 58DACF1E2C9E2ECE00664937 /* Debug.xcconfig */; };
		58DACF212C9E2ECE00664937 /* Release.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 58DACF1F2C9E2ECE00664937 /* Release.xcconfig */; };
		58E13CE92CDAEB61007F1210 /* GoogleMaps in Frameworks */ = {isa = PBXBuildFile; productRef = 58E13CE82CDAEB61007F1210 /* GoogleMaps */; };
		58E13CEC2CDAEBB5007F1210 /* GoogleMapsUtils in Frameworks */ = {isa = PBXBuildFile; productRef = 58E13CEB2CDAEBB5007F1210 /* GoogleMapsUtils */; };
		58E13CEE2CDAEC04007F1210 /* GoogleMap.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58E13CED2CDAEC04007F1210 /* GoogleMap.swift */; };
		58E13CF02CDB7DD1007F1210 /* LocationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58E13CEF2CDB7DD1007F1210 /* LocationManager.swift */; };
		7555FF83242A565900829871 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7555FF82242A565900829871 /* ContentView.swift */; };
		C98CCA9FD0F556E704236CA0 /* SentryLogger.swift in Sources */ = {isa = PBXBuildFile; fileRef = C98CC057FF70F7A94560708E /* SentryLogger.swift */; };
		C98CCF64845DD8BB67A3E6A3 /* AppleAnalytics.swift in Sources */ = {isa = PBXBuildFile; fileRef = C98CCA5351B0C568D0A6027B /* AppleAnalytics.swift */; };
		E1507E91C16C4794888C842B /* Sentry in Frameworks */ = {isa = PBXBuildFile; productRef = 87DFA0F460294F61AFDC1588 /* Sentry */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		7555FFB4242A642300829871 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		058557BA273AAA24004C7B11 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		058557D8273AAEEB004C7B11 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		2152FB032600AC8F00CF470E /* iOSApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = iOSApp.swift; sourceTree = "<group>"; };
		58033CFB2D437DB700A97874 /* BiometricAuth.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BiometricAuth.swift; sourceTree = "<group>"; };
		58072E362CDD90C000B59193 /* GoogleTokenProviderImpl.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GoogleTokenProviderImpl.swift; sourceTree = "<group>"; };
		58A8DBF12CA21EC60084B125 /* Environment.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Environment.swift; sourceTree = "<group>"; };
		58DACF1E2C9E2ECE00664937 /* Debug.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = Debug.xcconfig; sourceTree = "<group>"; };
		58DACF1F2C9E2ECE00664937 /* Release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = Release.xcconfig; sourceTree = "<group>"; };
		58E13CED2CDAEC04007F1210 /* GoogleMap.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GoogleMap.swift; sourceTree = "<group>"; };
		58E13CEF2CDB7DD1007F1210 /* LocationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LocationManager.swift; sourceTree = "<group>"; };
		7555FF7B242A565900829871 /* superdiary.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = superdiary.app; sourceTree = BUILT_PRODUCTS_DIR; };
		7555FF82242A565900829871 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		7555FF8C242A565B00829871 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		C98CC057FF70F7A94560708E /* SentryLogger.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SentryLogger.swift; sourceTree = "<group>"; };
		C98CCA5351B0C568D0A6027B /* AppleAnalytics.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppleAnalytics.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		7555FF78242A565900829871 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				587B21382CDC0B6B0081E14F /* GoogleSignInSwift in Frameworks */,
				58E13CEC2CDAEBB5007F1210 /* GoogleMapsUtils in Frameworks */,
				587B21362CDC0B6B0081E14F /* GoogleSignIn in Frameworks */,
				58E13CE92CDAEB61007F1210 /* GoogleMaps in Frameworks */,
				E1507E91C16C4794888C842B /* Sentry in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		058557D7273AAEEB004C7B11 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				058557D8273AAEEB004C7B11 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		581AA4922C962D1F009DD061 /* logging */ = {
			isa = PBXGroup;
			children = (
				C98CC057FF70F7A94560708E /* SentryLogger.swift */,
			);
			path = logging;
			sourceTree = "<group>";
		};
		587B21332CDC0B160081E14F /* auth */ = {
			isa = PBXGroup;
			children = (
				58072E362CDD90C000B59193 /* GoogleTokenProviderImpl.swift */,
			);
			path = auth;
			sourceTree = "<group>";
		};
		58DACF1D2C9E2E7400664937 /* Configurations */ = {
			isa = PBXGroup;
			children = (
				58DACF1E2C9E2ECE00664937 /* Debug.xcconfig */,
				58DACF1F2C9E2ECE00664937 /* Release.xcconfig */,
			);
			path = Configurations;
			sourceTree = "<group>";
		};
		7555FF72242A565900829871 = {
			isa = PBXGroup;
			children = (
				58DACF1D2C9E2E7400664937 /* Configurations */,
				7555FF7D242A565900829871 /* iosApp */,
				7555FF7C242A565900829871 /* Products */,
				7555FFB0242A642200829871 /* Frameworks */,
				C98CC691164B3AD1D7238F3B /* xcuserdata */,
			);
			sourceTree = "<group>";
		};
		7555FF7C242A565900829871 /* Products */ = {
			isa = PBXGroup;
			children = (
				7555FF7B242A565900829871 /* superdiary.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		7555FF7D242A565900829871 /* iosApp */ = {
			isa = PBXGroup;
			children = (
				058557BA273AAA24004C7B11 /* Assets.xcassets */,
				7555FF82242A565900829871 /* ContentView.swift */,
				7555FF8C242A565B00829871 /* Info.plist */,
				2152FB032600AC8F00CF470E /* iOSApp.swift */,
				581AA4922C962D1F009DD061 /* logging */,
				058557D7273AAEEB004C7B11 /* Preview Content */,
				C98CCCB8C5D439AAD6E28F35 /* analytics */,
				58A8DBF12CA21EC60084B125 /* Environment.swift */,
				58E13CED2CDAEC04007F1210 /* GoogleMap.swift */,
				58E13CEF2CDB7DD1007F1210 /* LocationManager.swift */,
				587B21332CDC0B160081E14F /* auth */,
				58033CFB2D437DB700A97874 /* BiometricAuth.swift */,
			);
			path = iosApp;
			sourceTree = "<group>";
		};
		7555FFB0242A642200829871 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		C98CC691164B3AD1D7238F3B /* xcuserdata */ = {
			isa = PBXGroup;
			children = (
				C98CC90884540F1CBDCD803F /* rafsanjani.xcuserdatad */,
			);
			name = xcuserdata;
			path = iosApp.xcodeproj/project.xcworkspace/xcuserdata;
			sourceTree = "<group>";
		};
		C98CC90884540F1CBDCD803F /* rafsanjani.xcuserdatad */ = {
			isa = PBXGroup;
			children = (
			);
			path = rafsanjani.xcuserdatad;
			sourceTree = "<group>";
		};
		C98CCCB8C5D439AAD6E28F35 /* analytics */ = {
			isa = PBXGroup;
			children = (
				C98CCA5351B0C568D0A6027B /* AppleAnalytics.swift */,
			);
			path = analytics;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		7555FF7A242A565900829871 /* superdiary */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7555FFA5242A565B00829871 /* Build configuration list for PBXNativeTarget "superdiary" */;
			buildPhases = (
				7555FFB5242A651A00829871 /* ShellScript */,
				7555FF77242A565900829871 /* Sources */,
				7555FF78242A565900829871 /* Frameworks */,
				7555FF79242A565900829871 /* Resources */,
				7555FFB4242A642300829871 /* Embed Frameworks */,
				3E2F6D63376F4C2C9F19F897 /* Upload Debug Symbols to Sentry */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = superdiary;
			packageProductDependencies = (
				87DFA0F460294F61AFDC1588 /* Sentry */,
				58E13CE82CDAEB61007F1210 /* GoogleMaps */,
				58E13CEB2CDAEBB5007F1210 /* GoogleMapsUtils */,
				587B21352CDC0B6B0081E14F /* GoogleSignIn */,
				587B21372CDC0B6B0081E14F /* GoogleSignInSwift */,
			);
			productName = iosApp;
			productReference = 7555FF7B242A565900829871 /* superdiary.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		7555FF73242A565900829871 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastSwiftUpdateCheck = 1130;
				LastUpgradeCheck = 1500;
				ORGANIZATIONNAME = orgName;
				TargetAttributes = {
					7555FF7A242A565900829871 = {
						CreatedOnToolsVersion = 11.3.1;
					};
				};
			};
			buildConfigurationList = 7555FF76242A565900829871 /* Build configuration list for PBXProject "iosApp" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 7555FF72242A565900829871;
			packageReferences = (
				7E27683A6CF243FD932E01C2 /* XCRemoteSwiftPackageReference "sentry-cocoa" */,
				58E13CE72CDAEB61007F1210 /* XCRemoteSwiftPackageReference "ios-maps-sdk" */,
				58E13CEA2CDAEBB5007F1210 /* XCRemoteSwiftPackageReference "google-maps-ios-utils" */,
				587B21342CDC0B6B0081E14F /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */,
			);
			productRefGroup = 7555FF7C242A565900829871 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				7555FF7A242A565900829871 /* superdiary */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		7555FF79242A565900829871 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				58DACF202C9E2ECE00664937 /* Debug.xcconfig in Resources */,
				58DACF212C9E2ECE00664937 /* Release.xcconfig in Resources */,
				058557D9273AAEEB004C7B11 /* Preview Assets.xcassets in Resources */,
				058557BB273AAA24004C7B11 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		3E2F6D63376F4C2C9F19F897 /* Upload Debug Symbols to Sentry */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}",
			);
			name = "Upload Debug Symbols to Sentry";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# This script is responsable to upload debug symbols and source context for Sentry.\nsource ~/.zshrc\nif which sentry-cli >/dev/null; then\nexport SENTRY_ORG=rafsanjani-inc\nexport SENTRY_PROJECT=superdiary-ios\nif [ ! $? -eq 0 ]; then\necho \"warning: sentry-cli - $ERROR\"\nfi\nelse\necho \"warning: sentry-cli not installed, download from https://github.com/getsentry/sentry-cli/releases\"\nfi\n";
		};
		7555FFB5242A651A00829871 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "cd \"$SRCROOT/..\"\n./gradlew :shared-ui:embedAndSignAppleFrameworkForXcode\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		7555FF77242A565900829871 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				58A8DBF22CA21EC60084B125 /* Environment.swift in Sources */,
				58E13CEE2CDAEC04007F1210 /* GoogleMap.swift in Sources */,
				2152FB042600AC8F00CF470E /* iOSApp.swift in Sources */,
				58072E372CDD90C000B59193 /* GoogleTokenProviderImpl.swift in Sources */,
				58E13CF02CDB7DD1007F1210 /* LocationManager.swift in Sources */,
				7555FF83242A565900829871 /* ContentView.swift in Sources */,
				58033CFC2D437DB700A97874 /* BiometricAuth.swift in Sources */,
				C98CCF64845DD8BB67A3E6A3 /* AppleAnalytics.swift in Sources */,
				C98CCA9FD0F556E704236CA0 /* SentryLogger.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		7555FFA3242A565B00829871 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 58DACF1E2C9E2ECE00664937 /* Debug.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.1;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		7555FFA4242A565B00829871 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 58DACF1F2C9E2ECE00664937 /* Release.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.1;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		7555FFA6242A565B00829871 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 58DACF1E2C9E2ECE00664937 /* Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_STYLE = Automatic;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_ASSET_PATHS = "\"iosApp/Preview Content\"";
				DEVELOPMENT_TEAM = JJ8NPE3EB2;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/../shared-ui/build/xcode-frameworks/$(CONFIGURATION)/$(SDK_NAME)",
				);
				INFOPLIST_FILE = iosApp/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					shared,
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.foreverrafs.superdiary;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		7555FFA7242A565B00829871 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 58DACF1F2C9E2ECE00664937 /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_STYLE = Automatic;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_ASSET_PATHS = "\"iosApp/Preview Content\"";
				DEVELOPMENT_TEAM = JJ8NPE3EB2;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/../shared-ui/build/xcode-frameworks/$(CONFIGURATION)/$(SDK_NAME)",
				);
				INFOPLIST_FILE = iosApp/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					shared,
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.foreverrafs.superdiary;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7555FF76242A565900829871 /* Build configuration list for PBXProject "iosApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7555FFA3242A565B00829871 /* Debug */,
				7555FFA4242A565B00829871 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7555FFA5242A565B00829871 /* Build configuration list for PBXNativeTarget "superdiary" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7555FFA6242A565B00829871 /* Debug */,
				7555FFA7242A565B00829871 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		587B21342CDC0B6B0081E14F /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/google/GoogleSignIn-iOS";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.0.0;
			};
		};
		58E13CE72CDAEB61007F1210 /* XCRemoteSwiftPackageReference "ios-maps-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/googlemaps/ios-maps-sdk";
			requirement = {
				kind = exactVersion;
				version = 9.1.1;
			};
		};
		58E13CEA2CDAEBB5007F1210 /* XCRemoteSwiftPackageReference "google-maps-ios-utils" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/googlemaps/google-maps-ios-utils.git";
			requirement = {
				kind = exactVersion;
				version = 6.0.0;
			};
		};
		7E27683A6CF243FD932E01C2 /* XCRemoteSwiftPackageReference "sentry-cocoa" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/getsentry/sentry-cocoa/";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		587B21352CDC0B6B0081E14F /* GoogleSignIn */ = {
			isa = XCSwiftPackageProductDependency;
			package = 587B21342CDC0B6B0081E14F /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignIn;
		};
		587B21372CDC0B6B0081E14F /* GoogleSignInSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = 587B21342CDC0B6B0081E14F /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignInSwift;
		};
		58E13CE82CDAEB61007F1210 /* GoogleMaps */ = {
			isa = XCSwiftPackageProductDependency;
			package = 58E13CE72CDAEB61007F1210 /* XCRemoteSwiftPackageReference "ios-maps-sdk" */;
			productName = GoogleMaps;
		};
		58E13CEB2CDAEBB5007F1210 /* GoogleMapsUtils */ = {
			isa = XCSwiftPackageProductDependency;
			package = 58E13CEA2CDAEBB5007F1210 /* XCRemoteSwiftPackageReference "google-maps-ios-utils" */;
			productName = GoogleMapsUtils;
		};
		87DFA0F460294F61AFDC1588 /* Sentry */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7E27683A6CF243FD932E01C2 /* XCRemoteSwiftPackageReference "sentry-cocoa" */;
			productName = Sentry;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 7555FF73242A565900829871 /* Project object */;
}
