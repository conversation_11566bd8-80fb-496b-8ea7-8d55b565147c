{"originHash": "3a84996c0b9b1534fd00096ea5cdda925c0926607cf7eb19628f34a9bdf83367", "pins": [{"identity": "app-check", "kind": "remoteSourceControl", "location": "https://github.com/google/app-check.git", "state": {"revision": "61b85103a1aeed8218f17c794687781505fbbef5", "version": "11.2.0"}}, {"identity": "appauth-ios", "kind": "remoteSourceControl", "location": "https://github.com/openid/AppAuth-iOS.git", "state": {"revision": "c89ed571ae140f8eb1142735e6e23d7bb8c34cb2", "version": "1.7.5"}}, {"identity": "google-maps-ios-utils", "kind": "remoteSourceControl", "location": "https://github.com/googlemaps/google-maps-ios-utils.git", "state": {"revision": "3234152e287e3593412e0c9ae72fc7b6cad2aec6", "version": "6.0.0"}}, {"identity": "googlesignin-ios", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleSignIn-iOS", "state": {"revision": "65fb3f1aa6ffbfdc79c4e22178a55cd91561f5e9", "version": "8.0.0"}}, {"identity": "googleutilities", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleUtilities.git", "state": {"revision": "53156c7ec267db846e6b64c9f4c4e31ba4cf75eb", "version": "8.0.2"}}, {"identity": "gtm-session-fetcher", "kind": "remoteSourceControl", "location": "https://github.com/google/gtm-session-fetcher.git", "state": {"revision": "a2ab612cb980066ee56d90d60d8462992c07f24b", "version": "3.5.0"}}, {"identity": "gtmappauth", "kind": "remoteSourceControl", "location": "https://github.com/google/GTMAppAuth.git", "state": {"revision": "5d7d66f647400952b1758b230e019b07c0b4b22a", "version": "4.1.1"}}, {"identity": "ios-maps-sdk", "kind": "remoteSourceControl", "location": "https://github.com/googlemaps/ios-maps-sdk", "state": {"revision": "3786e5028d868482ef88140117d33efd60828300", "version": "9.1.1"}}, {"identity": "promises", "kind": "remoteSourceControl", "location": "https://github.com/google/promises.git", "state": {"revision": "540318ecedd63d883069ae7f1ed811a2df00b6ac", "version": "2.4.0"}}, {"identity": "sentry-cocoa", "kind": "remoteSourceControl", "location": "https://github.com/getsentry/sentry-cocoa/", "state": {"revision": "5a601a24aa1b3fb29fcdb824633f050e08257e92", "version": "8.30.1"}}], "version": 3}