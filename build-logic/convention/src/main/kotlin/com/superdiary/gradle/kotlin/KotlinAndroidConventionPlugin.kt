// Copyright 2023, <PERSON> and the Tivi project contributors
// SPDX-License-Identifier: Apache-2.0

package com.superdiary.gradle.kotlin

import org.gradle.api.Plugin
import org.gradle.api.Project

class KotlinAndroidConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply("org.jetbrains.kotlin.android")
            }

            configureKotlin()
        }
    }
}
