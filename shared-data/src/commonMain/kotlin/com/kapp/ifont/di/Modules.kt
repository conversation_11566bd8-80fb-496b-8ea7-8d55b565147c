package com.kapp.ifont.di

import com.kapp.ifont.data.datasource.LocalFontDataSource
import com.kapp.ifont.data.datasource.remote.FontApi
import com.kapp.ifont.data.datasource.remote.RemoteFontApi
import com.kapp.ifont.database.di.databaseModule
import com.kapp.ifont.domain.repository.FontDataSource
import kotlinx.coroutines.InternalCoroutinesApi
import org.koin.core.module.dsl.bind
import org.koin.core.module.dsl.factoryOf
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.module

@OptIn(InternalCoroutinesApi::class)
val fontUseCaseModule = module {
    includes(databaseModule())

    // The local datasource will get injected by default
    singleOf(::LocalFontDataSource) { bind<FontDataSource>() }
    factoryOf(::RemoteFontApi) { bind<FontApi>() }
}
