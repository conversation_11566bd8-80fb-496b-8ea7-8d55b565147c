package com.kapp.ifont.domain.model

import com.kapp.ifont.database.model.FontInfoDb

data class FontInfo(
    val id: Long?,
    val version: Long,
    val name: String,
    val user: String,
    val userDesc: String?,
    val url: String,
    val purl: String,
    val thumburl: String,
    val size: Long,
    val locale: String,
    val type: String,
    val label: String?,
    val showPos: Long,
)

fun FontInfo.toDatabase(): FontInfoDb = FontInfoDb(
    id = id,
    version = version,
    name = name,
    user = user,
    userDesc = userDesc,
    url = url,
    purl = purl,
    thumburl = thumburl,
    size = size,
    locale = locale,
    type = type,
    label = label,
    showPos = showPos
)
