package com.kapp.ifont.data.datasource.remote

import com.kapp.ifont.data.model.FontInfoDto
import io.github.jan.supabase.SupabaseClient
import io.github.jan.supabase.annotations.SupabaseExperimental
import io.github.jan.supabase.postgrest.from
import io.github.jan.supabase.realtime.selectAsFlow

import kotlinx.coroutines.flow.Flow

class RemoteFontApi(
    private val supabase: SupabaseClient,
) : FontApi {
    @OptIn(SupabaseExperimental::class)
    override fun fetchAll(): Flow<List<FontInfoDto>> = supabase.from(TABLE_NAME)
        .selectAsFlow(FontInfoDto::id)

    companion object {
        private const val TABLE_NAME = "font"
    }
}
