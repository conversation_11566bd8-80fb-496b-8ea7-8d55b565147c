package com.kapp.ifont.data.mapper

import com.kapp.ifont.database.model.FontInfoDb
import com.kapp.ifont.domain.model.FontInfo

fun FontInfoDb.toFont(): FontInfo = FontInfo(
    id = id,
    version = version,
    name = name,
    user = user,
    userDesc = userDesc,
    url = url,
    purl = purl,
    thumburl = thumburl,
    size = size,
    locale = locale,
    type = type,
    label = label,
    showPos = showPos
)
