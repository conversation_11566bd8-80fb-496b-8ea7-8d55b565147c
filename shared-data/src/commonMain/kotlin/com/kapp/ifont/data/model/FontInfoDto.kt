package com.kapp.ifont.data.model

import com.kapp.ifont.domain.model.FontInfo
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class FontInfoDto(
    @SerialName("id")
    val id: Long,
    @SerialName("version")
    val version: Long,
    @SerialName("name")
    val name: String,
    @SerialName("user")
    val user: String,
    @SerialName("userDesc")
    val userDesc: String?,
    @SerialName("url")
    val url: String,
    @SerialName("purl")
    val purl: String,
    @SerialName("thumburl")
    val thumburl: String,
    @SerialName("size")
    val size: Long,
    @SerialName("locale")
    val locale: String,
    @SerialName("type")
    val type: String,
    @SerialName("label")
    val label: String?,
    @SerialName("language")
    val language: String,
    @SerialName("showPos")
    val showPos: Long,
)

fun FontInfoDto.toFontInfo(): FontInfo =  FontInfo(
    id = id,
    version = version,
    name = name,
    user = user,
    userDesc = userDesc,
    url = url,
    purl = purl,
    thumburl = thumburl,
    size = size,
    locale = locale,
    type = type,
    label = label,
    showPos = showPos
)
