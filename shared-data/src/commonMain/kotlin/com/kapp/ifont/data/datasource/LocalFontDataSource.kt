package com.kapp.ifont.data.datasource

import com.foreverrafs.superdiary.domain.model.toDatabase
import com.kapp.ifont.data.mapper.toFont
import com.kapp.ifont.database.FontDatabase
import com.kapp.ifont.database.model.FontInfoDb
import com.kapp.ifont.domain.model.FontInfo
import com.kapp.ifont.domain.model.toDatabase
import com.kapp.ifont.domain.repository.FontDataSource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.map

class LocalFontDataSource(private val database: FontDatabase) : FontDataSource {
    override fun fetchAll(): Flow<List<FontInfo>> = database.getAllFonts().mapToFont()
    override suspend fun addAll(fonts: List<FontInfo>): Long =
        database.insert(fonts.map { it.toDatabase() })

    private fun Flow<List<FontInfoDb>>?.mapToFont() =
        this?.map { diaryDtoList -> diaryDtoList.map { it.toFont() } }
            ?: emptyFlow()

}
