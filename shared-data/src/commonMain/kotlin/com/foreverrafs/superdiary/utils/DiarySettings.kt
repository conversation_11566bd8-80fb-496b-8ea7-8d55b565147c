package com.foreverrafs.superdiary.utils

import com.foreverrafs.preferences.Preference
import com.foreverrafs.preferences.PreferenceKey

@Preference("DiaryPreference")
data class DiarySettings(
    @PreferenceKey.Boolean(default = true)
    val isFirstLaunch: <PERSON><PERSON><PERSON>,

    @PreferenceKey.Boolean(default = true)
    val showWeeklySummary: <PERSON><PERSON><PERSON>,

    @PreferenceKey.Boolean(default = true)
    val showAtAGlance: <PERSON><PERSON><PERSON>,

    @PreferenceKey.Boolean(default = true)
    val showLatestEntries: <PERSON><PERSON><PERSON>,

    @PreferenceKey.Boolean(default = false)
    val isBiometricAuthEnabled: <PERSON><PERSON><PERSON>,

    @PreferenceKey.Boolean(default = true)
    val showLocationPermissionDialog: <PERSON><PERSON><PERSON>,

    @PreferenceKey.Boolean(default = true)
    val showBiometricAuthDialog: <PERSON>olean,
) {
    companion object {
        val Empty: DiarySettings = DiarySettings(
            isFirstLaunch = true,
            showWeeklySummary = true,
            showAtAGlance = true,
            showLatestEntries = true,
            isBiometricAuthEnabled = false,
            showLocationPermissionDialog = true,
            showBiometricAuthDialog = true,
        )
    }
}
