#Gradle
org.gradle.jvmargs=-Xmx8192M -Dfile.encoding=UTF-8 -Dkotlin.daemon.jvm.options\="-Xmx8192M"
#Kotlin
kotlin.code.style=official
#Android
android.useAndroidX=true
android.nonTransitiveRClass=true
#Compose
org.jetbrains.compose.experimental.uikit.enabled=true
kotlin.native.ignoreDisabledTargets=true
#MPP
kotlin.mpp.enableCInteropCommonization=true
app.cash.paparazzi.legacy.resource.loading=true
app.cash.paparazzi.legacy.asset.loading=true
kotlin.mpp.androidSourceSetLayoutVersion=2
org.gradle.configuration-cache=false
