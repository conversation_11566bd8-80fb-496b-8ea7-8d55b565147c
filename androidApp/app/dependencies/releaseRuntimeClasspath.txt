androidx.activity:activity-compose:1.10.0
androidx.activity:activity-ktx:1.10.0
androidx.activity:activity:1.10.0
androidx.annotation:annotation-experimental:1.4.1
androidx.annotation:annotation-jvm:1.9.1
androidx.annotation:annotation:1.9.1
androidx.appcompat:appcompat-resources:1.7.0
androidx.appcompat:appcompat:1.7.0
androidx.arch.core:core-common:2.2.0
androidx.arch.core:core-runtime:2.2.0
androidx.autofill:autofill:1.0.0
androidx.biometric:biometric:1.4.0-alpha02
androidx.browser:browser:1.8.0
androidx.cardview:cardview:1.0.0
androidx.collection:collection-jvm:1.4.4
androidx.collection:collection-ktx:1.4.4
androidx.collection:collection:1.4.4
androidx.compose.animation:animation-android:1.7.6
androidx.compose.animation:animation-core-android:1.7.6
androidx.compose.animation:animation-core:1.7.6
androidx.compose.animation:animation:1.7.6
androidx.compose.foundation:foundation-android:1.7.6
androidx.compose.foundation:foundation-layout-android:1.7.6
androidx.compose.foundation:foundation-layout:1.7.6
androidx.compose.foundation:foundation:1.7.6
androidx.compose.material3:material3-android:1.3.1
androidx.compose.material3:material3:1.3.1
androidx.compose.material:material-android:1.7.5
androidx.compose.material:material-icons-core-android:1.7.6
androidx.compose.material:material-icons-core:1.7.6
androidx.compose.material:material-icons-extended-android:1.7.6
androidx.compose.material:material-icons-extended:1.7.6
androidx.compose.material:material-ripple-android:1.7.6
androidx.compose.material:material-ripple:1.7.6
androidx.compose.material:material:1.7.5
androidx.compose.runtime:runtime-android:1.7.6
androidx.compose.runtime:runtime-saveable-android:1.7.6
androidx.compose.runtime:runtime-saveable:1.7.6
androidx.compose.runtime:runtime:1.7.6
androidx.compose.ui:ui-android:1.7.6
androidx.compose.ui:ui-geometry-android:1.7.6
androidx.compose.ui:ui-geometry:1.7.6
androidx.compose.ui:ui-graphics-android:1.7.6
androidx.compose.ui:ui-graphics:1.7.6
androidx.compose.ui:ui-text-android:1.7.6
androidx.compose.ui:ui-text:1.7.6
androidx.compose.ui:ui-tooling-android:1.7.6
androidx.compose.ui:ui-tooling-data-android:1.7.6
androidx.compose.ui:ui-tooling-data:1.7.6
androidx.compose.ui:ui-tooling-preview-android:1.7.6
androidx.compose.ui:ui-tooling-preview:1.7.6
androidx.compose.ui:ui-tooling:1.7.6
androidx.compose.ui:ui-unit-android:1.7.6
androidx.compose.ui:ui-unit:1.7.6
androidx.compose.ui:ui-util-android:1.7.6
androidx.compose.ui:ui-util:1.7.6
androidx.compose.ui:ui:1.7.6
androidx.compose:compose-bom:2024.11.00
androidx.concurrent:concurrent-futures:1.1.0
androidx.constraintlayout:constraintlayout-solver:2.0.1
androidx.constraintlayout:constraintlayout:2.0.1
androidx.coordinatorlayout:coordinatorlayout:1.1.0
androidx.core:core-ktx:1.15.0
androidx.core:core:1.15.0
androidx.credentials:credentials-play-services-auth:1.3.0
androidx.credentials:credentials:1.3.0
androidx.cursoradapter:cursoradapter:1.0.0
androidx.customview:customview-poolingcontainer:1.0.0
androidx.customview:customview:1.1.0
androidx.databinding:viewbinding:8.4.2
androidx.datastore:datastore-android:1.1.2
androidx.datastore:datastore-core-android:1.1.2
androidx.datastore:datastore-core-okio-jvm:1.1.2
androidx.datastore:datastore-core-okio:1.1.2
androidx.datastore:datastore-core:1.1.2
androidx.datastore:datastore-preferences-android:1.1.2
androidx.datastore:datastore-preferences-core-jvm:1.1.2
androidx.datastore:datastore-preferences-core:1.1.2
androidx.datastore:datastore-preferences-external-protobuf:1.1.2
androidx.datastore:datastore-preferences-proto:1.1.2
androidx.datastore:datastore-preferences:1.1.2
androidx.datastore:datastore:1.1.2
androidx.documentfile:documentfile:1.0.0
androidx.drawerlayout:drawerlayout:1.1.1
androidx.dynamicanimation:dynamicanimation:1.0.0
androidx.emoji2:emoji2-views-helper:1.3.0
androidx.emoji2:emoji2:1.3.0
androidx.exifinterface:exifinterface:1.3.7
androidx.fragment:fragment-ktx:1.8.5
androidx.fragment:fragment:1.8.5
androidx.graphics:graphics-path:1.0.1
androidx.interpolator:interpolator:1.0.0
androidx.legacy:legacy-support-core-utils:1.0.0
androidx.lifecycle:lifecycle-common-java8:2.9.0-alpha03
androidx.lifecycle:lifecycle-common-jvm:2.9.0-alpha03
androidx.lifecycle:lifecycle-common:2.9.0-alpha03
androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0-alpha03
androidx.lifecycle:lifecycle-livedata-core:2.9.0-alpha03
androidx.lifecycle:lifecycle-livedata:2.9.0-alpha03
androidx.lifecycle:lifecycle-process:2.9.0-alpha03
androidx.lifecycle:lifecycle-runtime-android:2.9.0-alpha03
androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0-alpha03
androidx.lifecycle:lifecycle-runtime-compose:2.9.0-alpha03
androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0-alpha03
androidx.lifecycle:lifecycle-runtime-ktx:2.9.0-alpha03
androidx.lifecycle:lifecycle-runtime:2.9.0-alpha03
androidx.lifecycle:lifecycle-viewmodel-android:2.9.0-alpha03
androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0-alpha03
androidx.lifecycle:lifecycle-viewmodel-compose:2.9.0-alpha03
androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0-alpha03
androidx.lifecycle:lifecycle-viewmodel-savedstate:2.9.0-alpha03
androidx.lifecycle:lifecycle-viewmodel:2.9.0-alpha03
androidx.loader:loader:1.0.0
androidx.localbroadcastmanager:localbroadcastmanager:1.0.0
androidx.navigation:navigation-common-ktx:2.8.4
androidx.navigation:navigation-common:2.8.4
androidx.navigation:navigation-compose:2.8.4
androidx.navigation:navigation-runtime-ktx:2.8.4
androidx.navigation:navigation-runtime:2.8.4
androidx.print:print:1.0.0
androidx.profileinstaller:profileinstaller:1.4.1
androidx.recyclerview:recyclerview:1.1.0
androidx.resourceinspection:resourceinspection-annotation:1.0.1
androidx.savedstate:savedstate-ktx:1.2.1
androidx.savedstate:savedstate:1.2.1
androidx.sqlite:sqlite-framework:2.4.0
androidx.sqlite:sqlite:2.4.0
androidx.startup:startup-runtime:1.2.0
androidx.tracing:tracing:1.2.0
androidx.transition:transition:1.5.0
androidx.vectordrawable:vectordrawable-animated:1.1.0
androidx.vectordrawable:vectordrawable:1.1.0
androidx.versionedparcelable:versionedparcelable:1.1.1
androidx.viewpager2:viewpager2:1.0.0
androidx.viewpager:viewpager:1.0.0
app.cash.sqldelight:android-driver:2.0.2
app.cash.sqldelight:async-extensions-jvm:2.0.2
app.cash.sqldelight:async-extensions:2.0.2
app.cash.sqldelight:coroutines-extensions-jvm:2.0.2
app.cash.sqldelight:coroutines-extensions:2.0.2
app.cash.sqldelight:runtime-jvm:2.0.2
app.cash.sqldelight:runtime:2.0.2
co.touchlab:kermit-android:2.0.5
co.touchlab:kermit-core-android:2.0.5
co.touchlab:kermit-core:2.0.5
co.touchlab:kermit:2.0.5
co.touchlab:stately-common-jvm:2.1.0
co.touchlab:stately-common:2.1.0
co.touchlab:stately-concurrency-jvm:2.1.0
co.touchlab:stately-concurrency:2.1.0
co.touchlab:stately-concurrent-collections-jvm:2.1.0
co.touchlab:stately-concurrent-collections:2.1.0
co.touchlab:stately-strict-jvm:2.1.0
co.touchlab:stately-strict:2.1.0
com.aallam.openai:openai-client-jvm:4.0.1
com.aallam.openai:openai-client:4.0.1
com.aallam.openai:openai-core-jvm:4.0.1
com.aallam.openai:openai-core:4.0.1
com.benasher44:uuid-jvm:0.8.4
com.benasher44:uuid:0.8.4
com.google.accompanist:accompanist-drawablepainter:0.36.0
com.google.android.gms:play-services-auth-api-phone:18.0.2
com.google.android.gms:play-services-auth-base:18.0.10
com.google.android.gms:play-services-auth:21.1.1
com.google.android.gms:play-services-base:18.5.0
com.google.android.gms:play-services-basement:18.4.0
com.google.android.gms:play-services-fido:21.0.0
com.google.android.gms:play-services-location:21.3.0
com.google.android.gms:play-services-maps:19.0.0
com.google.android.gms:play-services-tasks:18.2.0
com.google.android.libraries.identity.googleid:googleid:1.1.1
com.google.android.material:material:1.12.0
com.google.errorprone:error_prone_annotations:2.15.0
com.google.guava:listenablefuture:1.0
com.google.maps.android:maps-compose:6.4.3
com.google.maps.android:maps-ktx:5.1.1
com.mohamedrejeb.ksoup:ksoup-entities-jvm:0.4.0
com.mohamedrejeb.ksoup:ksoup-entities:0.4.0
com.mohamedrejeb.ksoup:ksoup-html-jvm:0.4.0
com.mohamedrejeb.ksoup:ksoup-html:0.4.0
com.mohamedrejeb.richeditor:richeditor-compose-android:1.0.0-rc10
com.mohamedrejeb.richeditor:richeditor-compose:1.0.0-rc10
com.russhwolf:multiplatform-settings-android:1.3.0
com.russhwolf:multiplatform-settings-coroutines-android:1.3.0
com.russhwolf:multiplatform-settings-coroutines:1.3.0
com.russhwolf:multiplatform-settings-no-arg-android:1.3.0
com.russhwolf:multiplatform-settings-no-arg:1.3.0
com.russhwolf:multiplatform-settings:1.3.0
com.squareup.okio:okio-jvm:3.10.2
com.squareup.okio:okio:3.10.2
com.valentinilk.shimmer:compose-shimmer-android:1.3.2
com.valentinilk.shimmer:compose-shimmer:1.3.2
dev.icerock.moko:permissions-android:0.18.1
dev.icerock.moko:permissions-compose-android:0.18.1
dev.icerock.moko:permissions-compose:0.18.1
dev.icerock.moko:permissions:0.18.1
io.coil-kt.coil3:coil-android:3.0.4
io.coil-kt.coil3:coil-compose-android:3.0.4
io.coil-kt.coil3:coil-compose-core-android:3.0.4
io.coil-kt.coil3:coil-compose-core:3.0.4
io.coil-kt.coil3:coil-compose:3.0.4
io.coil-kt.coil3:coil-core-android:3.0.4
io.coil-kt.coil3:coil-core:3.0.4
io.coil-kt.coil3:coil-network-core-android:3.0.4
io.coil-kt.coil3:coil-network-core:3.0.4
io.coil-kt.coil3:coil-network-ktor3-android:3.0.4
io.coil-kt.coil3:coil-network-ktor3:3.0.4
io.coil-kt.coil3:coil:3.0.4
io.github.jan-tennert.supabase:auth-kt-android:3.1.0
io.github.jan-tennert.supabase:auth-kt:3.1.0
io.github.jan-tennert.supabase:bom:3.1.0
io.github.jan-tennert.supabase:compose-auth-android:3.1.0
io.github.jan-tennert.supabase:compose-auth:3.1.0
io.github.jan-tennert.supabase:postgrest-kt-android:3.1.0
io.github.jan-tennert.supabase:postgrest-kt:3.1.0
io.github.jan-tennert.supabase:realtime-kt-android:3.1.0
io.github.jan-tennert.supabase:realtime-kt:3.1.0
io.github.jan-tennert.supabase:supabase-kt-android:3.1.0
io.github.jan-tennert.supabase:supabase-kt:3.1.0
io.insert-koin:koin-android:4.0.2
io.insert-koin:koin-compose-jvm:4.0.2
io.insert-koin:koin-compose-viewmodel-jvm:4.0.2
io.insert-koin:koin-compose-viewmodel:4.0.2
io.insert-koin:koin-compose:4.0.2
io.insert-koin:koin-core-jvm:4.0.2
io.insert-koin:koin-core-viewmodel-jvm:4.0.2
io.insert-koin:koin-core-viewmodel:4.0.2
io.insert-koin:koin-core:4.0.2
io.ktor:ktor-client-auth-jvm:3.0.0
io.ktor:ktor-client-auth:3.0.0
io.ktor:ktor-client-cio-jvm:3.0.3
io.ktor:ktor-client-cio:3.0.3
io.ktor:ktor-client-content-negotiation-jvm:3.0.3
io.ktor:ktor-client-content-negotiation:3.0.3
io.ktor:ktor-client-core-jvm:3.0.3
io.ktor:ktor-client-core:3.0.3
io.ktor:ktor-client-json-jvm:3.0.3
io.ktor:ktor-client-json:3.0.3
io.ktor:ktor-client-logging-jvm:3.0.0
io.ktor:ktor-client-logging:3.0.0
io.ktor:ktor-client-websockets-jvm:3.0.3
io.ktor:ktor-client-websockets:3.0.3
io.ktor:ktor-events-jvm:3.0.3
io.ktor:ktor-events:3.0.3
io.ktor:ktor-http-cio-jvm:3.0.3
io.ktor:ktor-http-cio:3.0.3
io.ktor:ktor-http-jvm:3.0.3
io.ktor:ktor-http:3.0.3
io.ktor:ktor-io-jvm:3.0.3
io.ktor:ktor-io:3.0.3
io.ktor:ktor-network-jvm:3.0.3
io.ktor:ktor-network-tls-jvm:3.0.3
io.ktor:ktor-network-tls:3.0.3
io.ktor:ktor-network:3.0.3
io.ktor:ktor-serialization-jvm:3.0.3
io.ktor:ktor-serialization-kotlinx-json-jvm:3.0.3
io.ktor:ktor-serialization-kotlinx-json:3.0.3
io.ktor:ktor-serialization-kotlinx-jvm:3.0.3
io.ktor:ktor-serialization-kotlinx:3.0.3
io.ktor:ktor-serialization:3.0.3
io.ktor:ktor-sse-jvm:3.0.3
io.ktor:ktor-sse:3.0.3
io.ktor:ktor-utils-jvm:3.0.3
io.ktor:ktor-utils:3.0.3
io.ktor:ktor-websocket-serialization-jvm:3.0.3
io.ktor:ktor-websocket-serialization:3.0.3
io.ktor:ktor-websockets-jvm:3.0.3
io.ktor:ktor-websockets:3.0.3
io.sentry:sentry-android-core:8.1.0
io.sentry:sentry-android-fragment:8.1.0
io.sentry:sentry-android-navigation:8.1.0
io.sentry:sentry-android-ndk:8.1.0
io.sentry:sentry-android-replay:8.1.0
io.sentry:sentry-android-sqlite:8.1.0
io.sentry:sentry-android:8.1.0
io.sentry:sentry-compose-android:8.1.0
io.sentry:sentry-kotlin-extensions:8.1.0
io.sentry:sentry-native-ndk:0.7.19
io.sentry:sentry:8.1.0
org.jetbrains.androidx.core:core-bundle-android:1.1.0-alpha02
org.jetbrains.androidx.core:core-bundle:1.1.0-alpha02
org.jetbrains.androidx.core:core-uri-android:1.1.0-alpha02
org.jetbrains.androidx.core:core-uri:1.1.0-alpha02
org.jetbrains.androidx.lifecycle:lifecycle-common:2.9.0-alpha02
org.jetbrains.androidx.lifecycle:lifecycle-runtime-compose:2.8.4
org.jetbrains.androidx.lifecycle:lifecycle-runtime:2.9.0-alpha02
org.jetbrains.androidx.lifecycle:lifecycle-viewmodel-compose:2.8.4
org.jetbrains.androidx.lifecycle:lifecycle-viewmodel-savedstate:2.9.0-alpha02
org.jetbrains.androidx.lifecycle:lifecycle-viewmodel:2.9.0-alpha02
org.jetbrains.androidx.navigation:navigation-common:2.8.0-alpha12
org.jetbrains.androidx.navigation:navigation-compose:2.8.0-alpha12
org.jetbrains.androidx.navigation:navigation-runtime:2.8.0-alpha12
org.jetbrains.androidx.savedstate:savedstate:1.2.2
org.jetbrains.compose.animation:animation-core:1.7.3
org.jetbrains.compose.animation:animation:1.7.3
org.jetbrains.compose.annotation-internal:annotation:1.7.3
org.jetbrains.compose.collection-internal:collection:1.7.3
org.jetbrains.compose.components:components-resources-android:1.7.3
org.jetbrains.compose.components:components-resources:1.7.3
org.jetbrains.compose.components:components-ui-tooling-preview-android:1.7.3
org.jetbrains.compose.components:components-ui-tooling-preview:1.7.3
org.jetbrains.compose.foundation:foundation-layout:1.7.3
org.jetbrains.compose.foundation:foundation:1.7.3
org.jetbrains.compose.material3:material3:1.7.3
org.jetbrains.compose.material:material-icons-core:1.7.3
org.jetbrains.compose.material:material-icons-extended:1.7.3
org.jetbrains.compose.material:material-ripple:1.7.3
org.jetbrains.compose.material:material:1.7.0
org.jetbrains.compose.runtime:runtime-saveable:1.7.3
org.jetbrains.compose.runtime:runtime:1.7.3
org.jetbrains.compose.ui:ui-geometry:1.7.3
org.jetbrains.compose.ui:ui-graphics:1.7.3
org.jetbrains.compose.ui:ui-text:1.7.3
org.jetbrains.compose.ui:ui-unit:1.7.3
org.jetbrains.compose.ui:ui-util:1.7.3
org.jetbrains.compose.ui:ui:1.7.3
org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.1.10
org.jetbrains.kotlin:kotlin-bom:1.8.22
org.jetbrains.kotlin:kotlin-parcelize-runtime:2.1.10
org.jetbrains.kotlin:kotlin-reflect:2.1.10
org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21
org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10
org.jetbrains.kotlin:kotlin-stdlib:2.1.10
org.jetbrains.kotlinx:atomicfu-jvm:0.27.0
org.jetbrains.kotlinx:atomicfu:0.27.0
org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.1
org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.10.1
org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.1
org.jetbrains.kotlinx:kotlinx-coroutines-core:1.10.1
org.jetbrains.kotlinx:kotlinx-coroutines-slf4j:1.10.1
org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm:1.10.1
org.jetbrains.kotlinx:kotlinx-coroutines-test:1.10.1
org.jetbrains.kotlinx:kotlinx-datetime-jvm:0.6.1
org.jetbrains.kotlinx:kotlinx-datetime:0.6.1
org.jetbrains.kotlinx:kotlinx-io-bytestring-jvm:0.5.4
org.jetbrains.kotlinx:kotlinx-io-bytestring:0.5.4
org.jetbrains.kotlinx:kotlinx-io-core-jvm:0.5.4
org.jetbrains.kotlinx:kotlinx-io-core:0.5.4
org.jetbrains.kotlinx:kotlinx-serialization-bom:1.8.0
org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.8.0
org.jetbrains.kotlinx:kotlinx-serialization-core:1.8.0
org.jetbrains.kotlinx:kotlinx-serialization-json-io-jvm:1.8.0
org.jetbrains.kotlinx:kotlinx-serialization-json-io:1.8.0
org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.8.0
org.jetbrains.kotlinx:kotlinx-serialization-json:1.8.0
org.jetbrains:annotations:23.0.0
org.jetbrains:markdown-jvm:0.7.3
org.jetbrains:markdown:0.7.3
org.kotlincrypto:secure-random-jvm:0.3.2
org.kotlincrypto:secure-random:0.3.2
org.slf4j:slf4j-api:2.0.16
