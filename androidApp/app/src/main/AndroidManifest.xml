<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <application
        android:name="com.foreverrafs.superdiary.DiaryApp"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/full_backup_content"
        android:icon="@mipmap/ic_launcher"
        android:label="${applicationName}"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Material3.DayNight.NoActionBar"
        android:usesCleartextTraffic="false"
        tools:targetApi="s">
        <!-- Required: set your sentry.io project identifier (DSN) -->
        <meta-data
            android:name="io.sentry.dsn"
            android:value="${sentryBaseUrl}" />

        <!-- enable automatic breadcrumbs for user interactions (clicks, swipes, scrolls) -->
        <meta-data
            android:name="io.sentry.traces.user-interaction.enable"
            android:value="true" />
        <!-- enable screenshot for crashes -->
        <meta-data
            android:name="io.sentry.attach-screenshot"
            android:value="true" />
        <!-- enable view hierarchy for crashes -->
        <meta-data
            android:name="io.sentry.attach-view-hierarchy"
            android:value="true" />

        <!-- Set the environment    -->
        <meta-data
            android:name="io.sentry.environment"
            android:value="${sentryEnvironment}" />

        <profileable
            android:shell="true"
            tools:targetApi="29" />

        <activity
            android:name="com.foreverrafs.superdiary.MainActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="api.nebulainnova.co.uk"
                    android:scheme="https" />
            </intent-filter>

        </activity>
    </application>

</manifest>
