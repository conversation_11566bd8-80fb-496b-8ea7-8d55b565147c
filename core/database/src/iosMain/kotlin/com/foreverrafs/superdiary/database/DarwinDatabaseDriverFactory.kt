package com.foreverrafs.superdiary.database

import app.cash.sqldelight.db.SqlDriver
import app.cash.sqldelight.driver.native.NativeSqliteDriver
import com.kapp.ifont.database.IFontDatabase

class DarwinDatabaseDriverFactory : DatabaseDriverFactory {
    override fun createSqlDriver(): SqlDriver =
        NativeSqliteDriver(SuperDiaryDatabase.Schema, toString())

    override fun createFontSqlDriver(): SqlDriver =
        NativeSqliteDriver(IFontDatabase.Schema, toString())
}
