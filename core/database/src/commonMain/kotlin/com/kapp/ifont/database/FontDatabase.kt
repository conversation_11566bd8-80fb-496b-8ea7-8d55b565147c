package com.kapp.ifont.database

import app.cash.sqldelight.coroutines.asFlow
import app.cash.sqldelight.coroutines.mapToList
import com.foreverrafs.superdiary.database.model.DiaryDb
import com.foreverrafs.superdiary.database.model.LocationDb
import com.kapp.ifont.database.model.FontInfoDb
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow

@Suppress("TooManyFunctions")
class FontDatabase(
    database: IFontDatabase,
) {
    private val queries = database.databaseQueries

    private val fontMapper =
        { id: Long,
          version: Long,
          name: String,
          user: String,
          userDesc: String?,
          url: String,
          purl: String,
          thumburl: String,
          size: Long,
          locale: String,
          type: String,
          label: String?,
          showPos: Long ->
            FontInfoDb(
                id = id,
                version = version,
                name = name,
                user = user,
                userDesc = userDesc,
                url = url,
                purl = purl,
                thumburl = thumburl,
                size = size,
                locale = locale,
                type = type,
                label = label,
                showPos = showPos,
            )
        }

    fun insert(font: FontInfoDb): Long {
        queries.insert(
            id = font.id,
            version = font.version,
            name = font.name,
            user = font.user,
            userDesc = font.userDesc,
            url = font.url,
            purl = font.purl,
            thumburl = font.thumburl,
            size = font.size,
            locale = font.locale,
            type = font.type,
            label = font.label,
            showPos = font.showPos,
        )

        return queries.lastInsertRowId().executeAsOne()
    }

    fun insert(fonts: List<FontInfoDb>): Long {
        val result = queries.transactionWithResult {
            fonts.forEach(::upsert)
            fonts.size.toLong()
        }

        return result
    }

    fun upsert(font: FontInfoDb): Long = if (font.id == null) {
        insert(font)
    } else {
        update(font)
    }

    fun update(font: FontInfoDb): Long {
        queries.update(
            id = font.id,
            version = font.version,
            name = font.name,
            user = font.user,
            userDesc = font.userDesc,
            url = font.url,
            purl = font.purl,
            thumburl = font.thumburl,
            size = font.size,
            locale = font.locale,
            type = font.type,
            label = font.label,
            showPos = font.showPos,
        )

        return queries.getAffectedRows().executeAsOne()
    }

    fun getAllFonts(): Flow<List<FontInfoDb>> = queries.selectAll(
        mapper = fontMapper,
    )
        .asFlow()
        .mapToList(Dispatchers.Main)

}
