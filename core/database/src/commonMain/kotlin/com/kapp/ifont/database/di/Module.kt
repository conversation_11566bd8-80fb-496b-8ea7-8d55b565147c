package com.kapp.ifont.database.di

import com.foreverrafs.superdiary.database.DatabaseDriverFactory
import com.kapp.ifont.database.FontDatabase
import com.kapp.ifont.database.IFontDatabase
import org.koin.core.module.Module
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.module

fun databaseModule(): Module = module {
    single {
        val driverFactory = get<DatabaseDriverFactory>()
        IFontDatabase(
            driver = driverFactory.createFontSqlDriver()
        )
    }
    singleOf(::FontDatabase)
}
