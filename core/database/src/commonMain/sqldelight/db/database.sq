selectAll:
SELECT * FROM diary ORDER BY date DESC;

insert:
INSERT INTO diary(id, entry, date, favorite, location, markForDelete, isSynced) VALUES (?, ?, ?, ?, ?, 0, ?);

delete:
DELETE FROM diary WHERE id=?;

findByEntry:
SELECT * FROM diary WHERE entry LIKE '%' || :name || '%' ORDER BY date ASC;

findById:
SELECT * FROM diary WHERE id=? LIMIT 1;

findByDate:
SELECT * FROM diary WHERE date = :date  ORDER BY date ASC;

findByDateRange:
SELECT * FROM diary WHERE date BETWEEN ? AND ?;

deleteAll:
DELETE FROM diary;

update:
REPLACE INTO diary (id, entry, date, favorite, markForDelete, location, isSynced) VALUES(?, ?,?, ?, ?, ?, ?);

getLatestEntries:
SELECT * FROM diary ORDER BY date DESC LIMIT ?;

getFavoriteDiaries:
SELECT * FROM diary WHERE diary.favorite == 1;

getAffectedRows:
SELECT changes();

countEntries:
SELECT COUNT(*) FROM diary;

insertSummary:
INSERT INTO summary(summary, date) VALUES (?, ?);

getWeeklySummary:
SELECT * FROM summary;

clearWeeklySummary:
DELETE FROM summary;

lastInsertRowId:
SELECT last_insert_rowid();

saveChat:
INSERT INTO chat(id, date, content, role) VALUES (?, ?,?,?);

clearChat:
DELETE FROM chat;

getChatMessages:
SELECT * FROM chat;

getPendingDeletes:
SELECT * FROM diary WHERE markForDelete = 1;

getPendingSyncs:
SELECT * FROM diary WHERE isSynced = 0;
