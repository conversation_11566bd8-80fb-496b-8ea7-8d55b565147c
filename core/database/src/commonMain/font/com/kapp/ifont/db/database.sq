selectAll:
SELECT * FROM font ORDER BY id DESC;

insert:
INSERT INTO font(id, version, name, user, userDesc, url, purl, thumburl, size, locale, type, label, showPos) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

update:
REPLACE INTO font (id, version, name, user, userDesc, url, purl, thumburl, size, locale, type, label, showPos) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);


lastInsertRowId:
SELECT last_insert_rowid();

getAffectedRows:
SELECT changes();
