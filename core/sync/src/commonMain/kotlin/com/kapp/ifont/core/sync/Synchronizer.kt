package com.kapp.ifont.core.sync

import com.foreverrafs.superdiary.common.utils.AppCoroutineDispatchers
import com.foreverrafs.superdiary.core.logging.AggregateLogger
import com.kapp.ifont.data.datasource.remote.FontApi
import com.kapp.ifont.data.model.toFontInfo
import com.kapp.ifont.domain.repository.FontDataSource
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

interface Synchronizer {
    suspend fun startListening()
    fun stopListening()
}

class FontSynchronizer(
    private val fontApi: FontApi,
    private val dataSource: FontDataSource,
    private val logger: AggregateLogger,
    private val appCoroutineDispatchers: AppCoroutineDispatchers,
) : Synchronizer {
    private val coroutineScope = CoroutineScope(appCoroutineDispatchers.io)
    private var diaryApiListeningJob: Job? = null

    override suspend fun startListening() {
        logger.i(TAG) {
            "Listening for remote database changes!"
        }

        diaryApiListeningJob = coroutineScope.launch(appCoroutineDispatchers.main) {
            // fetch all entries from remote
            fontApi.fetchAll().collect { fontDtoList ->
                // insert remote entries into database. This will set isSynced flag
                // to true and trigger an update for observers
                val savedEntries = dataSource.addAll(fontDtoList.map { it.toFontInfo() })

                logger.i(TAG) {
                    "Saved $savedEntries new items to local database"
                }
            }
        }
    }

    override fun stopListening() {
        logger.i(TAG) {
            "Stopped listening for remote database changes!"
        }

        diaryApiListeningJob?.cancel(
            CancellationException("Screen moved to paused state!"),
        )
        diaryApiListeningJob = null
    }

    companion object {
        private const val TAG = "DiarySynchronizer"
    }
}
